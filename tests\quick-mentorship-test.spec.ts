import { test, expect } from '@playwright/test';

test('Quick mentorship dashboard test', async ({ page }) => {
  // Navigate directly to mentorship dashboard
  await page.goto('http://localhost:5177/dashboard/mentorship');
  
  // Wait for page to load
  await page.waitForLoadState('networkidle');
  
  // Check if the page loads
  await expect(page.locator('text=Mentorship')).toBeVisible({ timeout: 10000 });
  
  // Take a screenshot for debugging
  await page.screenshot({ path: 'mentorship-dashboard.png' });
  
  console.log('Mentorship dashboard loaded successfully');
});
